import java.io.IOException;
import java.util.*;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.*;
import org.apache.hadoop.mapreduce.*;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;

/**
 * Find grandparent-grandchild relationships using MapReduce
 * Two-step process:
 * 1. Build parent-child relationships
 * 2. Find grandparent-grandchild relationships by joining parent-child with child-parent
 */
public class GrandparentGrandchild {

    // Job 1: Process parent-child relationships and create both directions
    public static class RelationshipMapper extends Mapper<LongWritable, Text, Text, Text> {
        
        @Override
        public void map(LongWritable key, Text value, Context context) 
                throws IOException, InterruptedException {
            
            String line = value.toString().trim();
            if (line.isEmpty()) return;
            
            String[] parts = line.split(",");
            if (parts.length != 2) return;
            
            String child = parts[0].trim();
            String parent = parts[1].trim();
            
            // Emit child -> parent relationship (for finding grandparents)
            context.write(new Text(child), new Text("P:" + parent));
            
            // Emit parent -> child relationship (for finding grandchildren)
            context.write(new Text(parent), new Text("C:" + child));
        }
    }
    
    public static class RelationshipReducer extends Reducer<Text, Text, Text, Text> {
        
        @Override
        public void reduce(Text key, Iterable<Text> values, Context context)
                throws IOException, InterruptedException {
            
            List<String> parents = new ArrayList<>();
            List<String> children = new ArrayList<>();
            
            for (Text value : values) {
                String val = value.toString();
                if (val.startsWith("P:")) {
                    parents.add(val.substring(2));
                } else if (val.startsWith("C:")) {
                    children.add(val.substring(2));
                }
            }
            
            String person = key.toString();
            
            // Output parent relationships for this person
            for (String parent : parents) {
                context.write(new Text(person), new Text("PARENT:" + parent));
            }
            
            // Output child relationships for this person
            for (String child : children) {
                context.write(new Text(person), new Text("CHILD:" + child));
            }
        }
    }
    
    // Job 2: Find grandparent-grandchild relationships
    public static class GrandparentMapper extends Mapper<LongWritable, Text, Text, Text> {
        
        @Override
        public void map(LongWritable key, Text value, Context context) 
                throws IOException, InterruptedException {
            
            String line = value.toString().trim();
            if (line.isEmpty()) return;
            
            String[] parts = line.split("\t");
            if (parts.length != 2) return;
            
            String person = parts[0];
            String relationship = parts[1];
            
            if (relationship.startsWith("PARENT:")) {
                String parent = relationship.substring(7);
                // Emit parent as key, person as potential grandchild
                context.write(new Text(parent), new Text("GC:" + person));
            } else if (relationship.startsWith("CHILD:")) {
                String child = relationship.substring(6);
                // Emit person as key, child as potential grandchild
                context.write(new Text(person), new Text("GP:" + child));
            }
        }
    }
    
    public static class GrandparentReducer extends Reducer<Text, Text, Text, Text> {
        
        @Override
        public void reduce(Text key, Iterable<Text> values, Context context)
                throws IOException, InterruptedException {
            
            List<String> potentialGrandchildren = new ArrayList<>();
            List<String> potentialGrandparents = new ArrayList<>();
            
            for (Text value : values) {
                String val = value.toString();
                if (val.startsWith("GC:")) {
                    potentialGrandchildren.add(val.substring(3));
                } else if (val.startsWith("GP:")) {
                    potentialGrandparents.add(val.substring(3));
                }
            }
            
            String person = key.toString();
            
            // If this person has both potential grandchildren and is a potential grandparent,
            // then we have grandparent-grandchild relationships
            for (String grandchild : potentialGrandchildren) {
                for (String grandparent : potentialGrandparents) {
                    // person is the middle generation (parent)
                    // grandparent -> person -> grandchild
                    context.write(new Text(grandparent + " -> " + grandchild), 
                                new Text("grandparent-grandchild"));
                }
            }
        }
    }
    
    public static void main(String[] args) throws Exception {
        if (args.length != 3) {
            System.err.println("Usage: GrandparentGrandchild <input path> <intermediate output> <final output>");
            System.exit(-1);
        }
        
        Configuration conf = new Configuration();
        
        // Job 1: Process relationships
        Job job1 = Job.getInstance(conf, "process relationships");
        job1.setJarByClass(GrandparentGrandchild.class);
        job1.setMapperClass(RelationshipMapper.class);
        job1.setReducerClass(RelationshipReducer.class);
        job1.setOutputKeyClass(Text.class);
        job1.setOutputValueClass(Text.class);
        
        FileInputFormat.addInputPath(job1, new Path(args[0]));
        FileOutputFormat.setOutputPath(job1, new Path(args[1]));
        
        boolean job1Success = job1.waitForCompletion(true);
        if (!job1Success) {
            System.exit(1);
        }
        
        // Job 2: Find grandparent-grandchild relationships
        Job job2 = Job.getInstance(conf, "find grandparent grandchild");
        job2.setJarByClass(GrandparentGrandchild.class);
        job2.setMapperClass(GrandparentMapper.class);
        job2.setReducerClass(GrandparentReducer.class);
        job2.setOutputKeyClass(Text.class);
        job2.setOutputValueClass(Text.class);
        
        FileInputFormat.addInputPath(job2, new Path(args[1]));
        FileOutputFormat.setOutputPath(job2, new Path(args[2]));
        
        System.exit(job2.waitForCompletion(true) ? 0 : 1);
    }
}
