#!/bin/bash

# Compile and run script for Topic 3 - Grandparent-Grandchild Relationship

echo "=== 题目3: 祖孙关系查找 ==="

# Set Hadoop environment variables
export HADOOP_HOME=/opt/hadoop
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64

# Add Hadoop to PATH
export PATH=$HADOOP_HOME/bin:$HADOOP_HOME/sbin:$PATH

# Set Hadoop classpath
export HADOOP_CLASSPATH=$JAVA_HOME/lib/tools.jar

echo "1. 编译Java程序..."

# Compile GrandparentGrandchild
echo "编译 GrandparentGrandchild.java..."
hadoop com.sun.tools.javac.Main GrandparentGrandchild.java
if [ $? -ne 0 ]; then
    echo "编译 GrandparentGrandchild.java 失败"
    exit 1
fi

# Create JAR file
echo "创建JAR文件..."
jar cf grandparent_grandchild.jar GrandparentGrandchild*.class
if [ $? -ne 0 ]; then
    echo "创建JAR文件失败"
    exit 1
fi

echo "2. 准备HDFS输入数据..."

# Create input directory in HDFS
hdfs dfs -mkdir -p /user/input/relationships
hdfs dfs -rm -r /user/output/relationships* 2>/dev/null

# Copy input data to HDFS
hdfs dfs -put ../测试数据/child-parent.txt /user/input/relationships/
if [ $? -ne 0 ]; then
    echo "上传输入数据失败"
    exit 1
fi

echo "3. 运行MapReduce作业..."

echo "查找祖孙关系..."
hadoop jar grandparent_grandchild.jar GrandparentGrandchild /user/input/relationships /user/output/relationships_intermediate /user/output/relationships_final
if [ $? -ne 0 ]; then
    echo "运行祖孙关系查找失败"
    exit 1
fi

echo "4. 查看结果..."

echo "4.1 中间结果 (关系处理):"
hdfs dfs -cat /user/output/relationships_intermediate/part-r-00000 | head -20

echo ""
echo "4.2 最终结果 (祖孙关系):"
hdfs dfs -cat /user/output/relationships_final/part-r-00000

echo "5. 下载结果到本地..."
mkdir -p results
hdfs dfs -get /user/output/relationships_intermediate/part-r-00000 results/relationships_intermediate.txt
hdfs dfs -get /user/output/relationships_final/part-r-00000 results/grandparent_grandchild_relationships.txt

echo "6. 统计祖孙关系数量..."
RELATIONSHIP_COUNT=$(hdfs dfs -cat /user/output/relationships_final/part-r-00000 | wc -l)
echo "找到的祖孙关系总数: $RELATIONSHIP_COUNT"

echo "题目3完成! 结果已保存到 results/ 目录"
