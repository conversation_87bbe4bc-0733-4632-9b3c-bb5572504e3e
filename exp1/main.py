from mpi4py import MPI
import math

def f(x):
    return math.sin(x**2 + x)

def main():
    comm = MPI.COMM_WORLD
    rank = comm.Get_rank()
    size = comm.Get_size()

    # 参数设置（主进程）
    a, b, n = 0.0, 2.0, 100000  # 默认区间[0, 2]，分成100000个梯形
    if rank == 0:
        pass

    # 广播参数到所有进程
    a = comm.bcast(a, root=0)
    b = comm.bcast(b, root=0)
    n = comm.bcast(n, root=0)

    h = (b - a) / n
    m = n - 1  # 中间点数量

    # 分配每个进程计算的范围
    chunk = (m + size - 1) // size  # 向上取整
    start = rank * chunk
    end = min(start + chunk - 1, m - 1)

    # 计算局部和
    sum_local = 0.0
    for k in range(start, end + 1):
        x = a + (k + 1) * h  # 中间点x_j对应k+1
        sum_local += f(x)

    # 将局部和汇总到主进程
    sum_total = comm.reduce(sum_local, op=MPI.SUM, root=0)

    # 主进程计算最终结果
    if rank == 0:
        fa = f(a)
        fb = f(b)
        integral = h * (0.5 * (fa + fb) + sum_total)
        print(f"积分区间: [{a}, {b}], 梯形数: {n}, 近似值: {integral:.6f}")

if __name__ == "__main__":
    main()