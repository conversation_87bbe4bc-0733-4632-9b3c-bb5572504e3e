#!/bin/bash

# Master script to run all distributed computing experiments

echo "=========================================="
echo "分布式计算课程实验 - 总运行脚本"
echo "=========================================="

# Make all scripts executable
chmod +x 题目2/compile_and_run.sh
chmod +x 题目3/compile_and_run.sh
chmod +x 题目4/compile_and_run.sh

echo "选择要运行的实验:"
echo "1. 题目2 - 学生成绩分析 (MapReduce)"
echo "2. 题目3 - 祖孙关系查找 (MapReduce)"
echo "3. 题目4 - 成绩分布分析 (Spark)"
echo "4. 运行所有实验"
echo "0. 退出"

read -p "请输入选择 (0-4): " choice

case $choice in
    1)
        echo "运行题目2..."
        cd 题目2
        ./compile_and_run.sh
        cd ..
        ;;
    2)
        echo "运行题目3..."
        cd 题目3
        ./compile_and_run.sh
        cd ..
        ;;
    3)
        echo "运行题目4..."
        cd 题目4
        ./compile_and_run.sh
        cd ..
        ;;
    4)
        echo "运行所有实验..."
        
        echo "开始运行题目2..."
        cd 题目2
        ./compile_and_run.sh
        cd ..
        
        echo ""
        echo "开始运行题目3..."
        cd 题目3
        ./compile_and_run.sh
        cd ..
        
        echo ""
        echo "开始运行题目4..."
        cd 题目4
        ./compile_and_run.sh
        cd ..
        
        echo ""
        echo "所有实验完成!"
        ;;
    0)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "实验完成! 查看各题目的results目录获取结果。"
