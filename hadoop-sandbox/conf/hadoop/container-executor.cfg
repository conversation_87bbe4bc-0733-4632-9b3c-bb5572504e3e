yarn.nodemanager.linux-container-executor.group=#configured value of yarn.nodemanager.linux-container-executor.group
banned.users=#comma separated list of users who can not run applications
min.user.id=1000#Prevent other super-users
allowed.system.users=##comma separated list of system users who CAN run applications
feature.tc.enabled=false

# The configs below deal with settings for Docker
#[docker]
#  module.enabled=## enable/disable the module. set to "true" to enable, disabled by default
#  docker.binary=/usr/bin/docker
#  docker.allowed.capabilities=## comma separated capabilities that can be granted, e.g CHOWN,DAC_OVERRIDE,FSETID,FOWNER,MKNOD,NET_RAW,SETGID,SETUID,SETFCAP,SETPCAP,NET_BIND_SERVICE,SYS_CHROOT,KILL,AUDIT_WRITE
#  docker.allowed.devices=## comma separated list of devices that can be mounted into a container
#  docker.allowed.networks=## comma separated networks that can be used. e.g bridge,host,none
#  docker.allowed.ro-mounts=## comma separated volumes that can be mounted as read-only
#  docker.allowed.rw-mounts=## comma separate volumes that can be mounted as read-write, add the yarn local and log dirs to this list to run Hadoop jobs
#  docker.privileged-containers.enabled=false
#  docker.allowed.volume-drivers=## comma separated list of allowed volume-drivers
#  docker.no-new-privileges.enabled=## enable/disable the no-new-privileges flag for docker run. Set to "true" to enable, disabled by default
#  docker.allowed.runtimes=## comma separated runtimes that can be used.

# The configs below deal with settings for FPGA resource
#[fpga]
#  module.enabled=## Enable/Disable the FPGA resource handler module. set to "true" to enable, disabled by default
#  fpga.major-device-number=## Major device number of FPGA, by default is 246. Strongly recommend setting this
#  fpga.allowed-device-minor-numbers=## Comma separated allowed minor device numbers, empty means all FPGA devices managed by YARN.

# The configs below deal with settings for resource handled by pluggable device plugin framework
#[devices]
#  module.enabled=## Enable/Disable the device resource handler module for isolation. Disabled by default.
#  devices.denied-numbers=## Blacklisted devices not permitted to use. The format is comma separated "majorNumber:minorNumber". For instance, "195:1,195:2". Leave it empty means default devices reported by device plugin are all allowed.

# The configs below deal with settings for GPU resource
#[gpu]
#  module.enabled=## Enable/Disable GPU resource handler module. Set to "true" to enable, disabled by default