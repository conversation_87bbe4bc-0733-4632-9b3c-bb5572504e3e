<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration>
  <property>
    <name>dfs.replication</name>
    <value>1</value>
    <description>Single datanode does not allow for replication</description>
  </property>
  <property>
    <name>dfs.namenode.rpc-address</name>
    <value>namenode:8020</value>
    <description>Namenode will be running on host "namenode"</description>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.domain.socket.path</name>
    <value>/run/hadoop-hdfs/dn_socket</value>
  </property>
</configuration>
