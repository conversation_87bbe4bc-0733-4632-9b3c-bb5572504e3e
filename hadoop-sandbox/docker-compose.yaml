version: '3'

services:

  namenode:
    image: ${DOCKER_ORG:-packet23}/${DOCKER_PREFIX:-}hadoop-hdfs-namenode:${DOCKER_TAG:-latest}
    volumes:
      - "./conf/hadoop:/hadoop/etc/hadoop:ro"
      - "./data/namenode:/data"
    restart: always
    hostname: namenode

  datanode:
    image: ${DOCKER_ORG:-packet23}/${DOCKER_PREFIX:-}hadoop-hdfs-datanode:${DOCKER_TAG:-latest}
    volumes:
      - "./conf/hadoop:/hadoop/etc/hadoop:ro"
      - "./data/hadoopnode/data:/data"
      - "dnsocket:/run/hadoop-hdfs"
    restart: always
    ipc: shareable
    hostname: hadoopnode
    depends_on:
      - namenode

  resourcemanager:
    image: ${DOCKER_ORG:-packet23}/${DOCKER_PREFIX:-}hadoop-yarn-resourcemanager:${DOCKER_TAG:-latest}
    volumes:
      - "./conf/hadoop:/hadoop/etc/hadoop:ro"
    restart: always
    hostname: resourcemanager
    depends_on:
      - namenode

  nodemanager:
    image: ${DOCKER_ORG:-packet23}/${DOCKER_PREFIX:-}hadoop-yarn-nodemanager:${DOCKER_TAG:-latest}
    volumes:
      - "./conf/hadoop:/hadoop/etc/hadoop:ro"
      - "./data/hadoopnode/data:/data"
      - "dnsocket:/run/hadoop-hdfs"
    restart: always
    network_mode: service:datanode
    ipc: service:datanode
    security_opt:
      - seccomp:unconfined
    cap_add:
      - SYS_ADMIN
      - SYSLOG
    depends_on:
      - namenode
      - datanode
      - resourcemanager

  jobhistoryserver:
    image: ${DOCKER_ORG:-packet23}/${DOCKER_PREFIX:-}hadoop-mapred-jobhistoryserver:${DOCKER_TAG:-latest}
    volumes:
      - "./conf/hadoop:/hadoop/etc/hadoop:ro"
    restart: always
    hostname: jobhistoryserver
    depends_on:
      - namenode
      - resourcemanager

  clientnode:
    image: ${DOCKER_ORG:-packet23}/${DOCKER_PREFIX:-}hadoop-client:${DOCKER_TAG:-latest}
    volumes:
      - "./conf/hadoop:/hadoop/etc/hadoop:ro"
      - "./data/clientnode/home:/home/<USER>"
      - "./data/clientnode/ssh:/etc/ssh"
    restart: always
    hostname: clientnode
    ports:
      - "127.0.0.1:2222:22"
    depends_on:
      - namenode
      - resourcemanager
      - datanode
      - nodemanager
      - jobhistoryserver

  front:
    image: httpd:2.4
    volumes:
      - "./conf/front/conf:/usr/local/apache2/conf:ro"
      - "./conf/front/htdocs:/usr/local/apache2/htdocs:ro"
    restart: always
    hostname: front
    ports:
      - "127.0.0.1:8042:8042"
      - "127.0.0.1:8080:8080"
      - "127.0.0.1:8088:8088"
      - "127.0.0.1:9864:9864"
      - "127.0.0.1:9870:9870"
      - "127.0.0.1:19888:19888"
    depends_on:
      - namenode
      - resourcemanager
      - datanode
      - nodemanager
      - jobhistoryserver

networks:
  default:
    driver: bridge

volumes:
  dnsocket:
    driver: local
