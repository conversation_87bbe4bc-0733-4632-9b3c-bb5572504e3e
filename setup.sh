#!/bin/bash

# Setup script for distributed computing experiments

echo "=========================================="
echo "分布式计算实验环境设置"
echo "=========================================="

# Make all scripts executable
echo "设置脚本执行权限..."
chmod +x run_all.sh
chmod +x 题目2/compile_and_run.sh
chmod +x 题目3/compile_and_run.sh
chmod +x 题目4/compile_and_run.sh
chmod +x 题目4/grade_distribution_analysis.py

echo "检查环境..."

# Check Java
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    echo "✓ Java版本: $JAVA_VERSION"
else
    echo "✗ Java未安装"
    exit 1
fi

# Check Hadoop
if command -v hadoop &> /dev/null; then
    HADOOP_VERSION=$(hadoop version 2>&1 | head -n 1)
    echo "✓ $HADOOP_VERSION"
else
    echo "✗ Hadoop未安装或未配置PATH"
fi

# Check Spark
if command -v spark-submit &> /dev/null; then
    SPARK_VERSION=$(spark-submit --version 2>&1 | grep "version" | head -n 1)
    echo "✓ $SPARK_VERSION"
else
    echo "✗ Spark未安装或未配置PATH"
fi

# Check HDFS
if command -v hdfs &> /dev/null; then
    echo "✓ HDFS命令可用"
    
    # Try to check HDFS status
    if hdfs dfsadmin -report &> /dev/null; then
        echo "✓ HDFS服务正常"
    else
        echo "⚠ HDFS服务可能未启动"
    fi
else
    echo "✗ HDFS命令不可用"
fi

# Check Python (optional for Spark)
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version)
    echo "✓ $PYTHON_VERSION"
else
    echo "⚠ Python3未安装 (Spark Python版本需要)"
fi

# Check Scala (optional for Spark)
if command -v scalac &> /dev/null; then
    SCALA_VERSION=$(scalac -version 2>&1)
    echo "✓ $SCALA_VERSION"
else
    echo "⚠ Scala未安装 (Spark Scala版本需要)"
fi

echo ""
echo "创建必要的目录..."
mkdir -p 题目2/results
mkdir -p 题目3/results  
mkdir -p 题目4/results

echo ""
echo "检查测试数据..."
if [ -f "测试数据/grades.txt" ]; then
    GRADE_LINES=$(wc -l < "测试数据/grades.txt")
    echo "✓ 成绩数据文件存在 ($GRADE_LINES 行)"
else
    echo "✗ 成绩数据文件不存在"
fi

if [ -f "测试数据/child-parent.txt" ]; then
    RELATION_LINES=$(wc -l < "测试数据/child-parent.txt")
    echo "✓ 关系数据文件存在 ($RELATION_LINES 行)"
else
    echo "✗ 关系数据文件不存在"
fi

echo ""
echo "环境设置完成!"
echo "运行 ./run_all.sh 开始实验"
