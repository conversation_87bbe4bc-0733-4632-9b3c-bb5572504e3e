import org.apache.spark.SparkConf;
import org.apache.spark.api.java.JavaPairRDD;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.api.java.function.Function;
import org.apache.spark.api.java.function.Function2;
import org.apache.spark.api.java.function.PairFunction;
import scala.Tuple2;
import scala.Tuple3;

import java.util.Arrays;
import java.util.List;

/**
 * Grade Distribution Analysis using Spark
 * Analyze required course average grades by class and score ranges:
 * 90-100, 80-89, 70-79, 60-69, below 60
 */
public class GradeDistributionAnalysis {
    
    public static void main(String[] args) {
        if (args.length != 2) {
            System.err.println("Usage: GradeDistributionAnalysis <input path> <output path>");
            System.exit(-1);
        }
        
        SparkConf conf = new SparkConf().setAppName("Grade Distribution Analysis");
        JavaSparkContext sc = new JavaSparkContext(conf);
        
        try {
            // Read input file
            JavaRDD<String> lines = sc.textFile(args[0]);
            
            // Filter and parse required courses
            JavaRDD<Tuple3<String, String, Double>> requiredCourses = lines
                .filter(new Function<String, Boolean>() {
                    @Override
                    public Boolean call(String line) throws Exception {
                        if (line.trim().isEmpty()) return false;
                        String[] fields = line.split(",");
                        return fields.length == 5 && "必修".equals(fields[3].trim());
                    }
                })
                .map(new Function<String, Tuple3<String, String, Double>>() {
                    @Override
                    public Tuple3<String, String, Double> call(String line) throws Exception {
                        String[] fields = line.split(",");
                        String className = fields[0].trim();
                        String studentName = fields[1].trim();
                        double grade = Double.parseDouble(fields[4].trim());
                        return new Tuple3<>(className, studentName, grade);
                    }
                });
            
            // Calculate average grade for each student in required courses
            JavaPairRDD<Tuple2<String, String>, Iterable<Double>> studentGrades = requiredCourses
                .mapToPair(new PairFunction<Tuple3<String, String, Double>, Tuple2<String, String>, Double>() {
                    @Override
                    public Tuple2<Tuple2<String, String>, Double> call(Tuple3<String, String, Double> record) throws Exception {
                        return new Tuple2<>(new Tuple2<>(record._1(), record._2()), record._3());
                    }
                })
                .groupByKey();
            
            // Calculate average grade for each student
            JavaPairRDD<String, Double> studentAverages = studentGrades
                .mapToPair(new PairFunction<Tuple2<Tuple2<String, String>, Iterable<Double>>, String, Double>() {
                    @Override
                    public Tuple2<String, Double> call(Tuple2<Tuple2<String, String>, Iterable<Double>> record) throws Exception {
                        String className = record._1()._1();
                        String studentName = record._1()._2();
                        
                        double sum = 0.0;
                        int count = 0;
                        for (Double grade : record._2()) {
                            sum += grade;
                            count++;
                        }
                        
                        double average = count > 0 ? sum / count : 0.0;
                        return new Tuple2<>(className, average);
                    }
                });
            
            // Categorize students by grade ranges
            JavaPairRDD<String, String> gradeCategories = studentAverages
                .mapToPair(new PairFunction<Tuple2<String, Double>, String, String>() {
                    @Override
                    public Tuple2<String, String> call(Tuple2<String, Double> record) throws Exception {
                        String className = record._1();
                        double average = record._2();
                        
                        String category;
                        if (average >= 90) {
                            category = "90-100";
                        } else if (average >= 80) {
                            category = "80-89";
                        } else if (average >= 70) {
                            category = "70-79";
                        } else if (average >= 60) {
                            category = "60-69";
                        } else {
                            category = "60以下";
                        }
                        
                        return new Tuple2<>(className + "," + category, "1");
                    }
                });
            
            // Count students in each category by class
            JavaPairRDD<String, Integer> categoryCounts = gradeCategories
                .mapToPair(new PairFunction<Tuple2<String, String>, String, Integer>() {
                    @Override
                    public Tuple2<String, Integer> call(Tuple2<String, String> record) throws Exception {
                        return new Tuple2<>(record._1(), 1);
                    }
                })
                .reduceByKey(new Function2<Integer, Integer, Integer>() {
                    @Override
                    public Integer call(Integer a, Integer b) throws Exception {
                        return a + b;
                    }
                });
            
            // Group by class and format output
            JavaPairRDD<String, Iterable<Tuple2<String, Integer>>> classResults = categoryCounts
                .mapToPair(new PairFunction<Tuple2<String, Integer>, String, Tuple2<String, Integer>>() {
                    @Override
                    public Tuple2<String, Tuple2<String, Integer>> call(Tuple2<String, Integer> record) throws Exception {
                        String[] parts = record._1().split(",");
                        String className = parts[0];
                        String category = parts[1];
                        return new Tuple2<>(className, new Tuple2<>(category, record._2()));
                    }
                })
                .groupByKey();
            
            // Format final output
            JavaRDD<String> finalResults = classResults
                .map(new Function<Tuple2<String, Iterable<Tuple2<String, Integer>>>, String>() {
                    @Override
                    public String call(Tuple2<String, Iterable<Tuple2<String, Integer>>> record) throws Exception {
                        String className = record._1();
                        
                        // Initialize counters for all categories
                        int count90_100 = 0;
                        int count80_89 = 0;
                        int count70_79 = 0;
                        int count60_69 = 0;
                        int countBelow60 = 0;
                        
                        // Count students in each category
                        for (Tuple2<String, Integer> categoryCount : record._2()) {
                            String category = categoryCount._1();
                            int count = categoryCount._2();
                            
                            switch (category) {
                                case "90-100":
                                    count90_100 = count;
                                    break;
                                case "80-89":
                                    count80_89 = count;
                                    break;
                                case "70-79":
                                    count70_79 = count;
                                    break;
                                case "60-69":
                                    count60_69 = count;
                                    break;
                                case "60以下":
                                    countBelow60 = count;
                                    break;
                            }
                        }
                        
                        return String.format("%s: 90-100分: %d人, 80-89分: %d人, 70-79分: %d人, 60-69分: %d人, 60分以下: %d人",
                                className, count90_100, count80_89, count70_79, count60_69, countBelow60);
                    }
                });
            
            // Save results
            finalResults.saveAsTextFile(args[1]);
            
            System.out.println("Grade distribution analysis completed successfully!");
            
        } catch (Exception e) {
            e.printStackTrace();
            System.exit(1);
        } finally {
            sc.close();
        }
    }
}
