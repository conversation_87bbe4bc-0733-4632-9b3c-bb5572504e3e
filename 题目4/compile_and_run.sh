#!/bin/bash

# Compile and run script for Topic 4 - Grade Distribution Analysis using Spark

echo "=== 题目4: 成绩分布分析 (Spark) ==="

# Set Spark environment variables
export SPARK_HOME=/opt/spark
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64

# Add Spark to PATH
export PATH=$SPARK_HOME/bin:$PATH

# Choose which version to run
echo "选择运行版本:"
echo "1. Java版本"
echo "2. Scala版本"
echo "3. Python版本"
read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo "运行Java版本..."

        echo "1. 编译Java程序..."
        # Set classpath for compilation
        SPARK_JARS="$SPARK_HOME/jars/*"

        # Compile GradeDistributionAnalysis
        echo "编译 GradeDistributionAnalysis.java..."
        javac -cp "$SPARK_JARS" GradeDistributionAnalysis.java
        if [ $? -ne 0 ]; then
            echo "编译 GradeDistributionAnalysis.java 失败"
            exit 1
        fi

        # Create JAR file
        echo "创建JAR文件..."
        jar cf grade_distribution_analysis.jar GradeDistributionAnalysis*.class
        if [ $? -ne 0 ]; then
            echo "创建JAR文件失败"
            exit 1
        fi

        PROGRAM="grade_distribution_analysis.jar"
        CLASS_NAME="GradeDistributionAnalysis"
        ;;

    2)
        echo "运行Scala版本..."

        echo "1. 编译Scala程序..."
        # Compile Scala program
        scalac -cp "$SPARK_HOME/jars/*" GradeDistributionAnalysis.scala
        if [ $? -ne 0 ]; then
            echo "编译 GradeDistributionAnalysis.scala 失败"
            exit 1
        fi

        # Create JAR file
        jar cf grade_distribution_analysis_scala.jar GradeDistributionAnalysis*.class
        if [ $? -ne 0 ]; then
            echo "创建JAR文件失败"
            exit 1
        fi

        PROGRAM="grade_distribution_analysis_scala.jar"
        CLASS_NAME="GradeDistributionAnalysis"
        ;;

    3)
        echo "运行Python版本..."
        PROGRAM="grade_distribution_analysis.py"
        CLASS_NAME=""
        ;;

    *)
        echo "无效选择，默认使用Python版本"
        PROGRAM="grade_distribution_analysis.py"
        CLASS_NAME=""
        ;;
esac

echo "2. 准备输入数据..."

# Create results directory
mkdir -p results

# Try HDFS first, fall back to local if not available
if command -v hdfs &> /dev/null; then
    echo "使用HDFS模式..."

    # Create input directory in HDFS
    hdfs dfs -mkdir -p /user/input/spark_grades 2>/dev/null
    hdfs dfs -rm -r /user/output/spark_grades* 2>/dev/null

    # Copy input data to HDFS
    hdfs dfs -put ../测试数据/grades.txt /user/input/spark_grades/ 2>/dev/null

    INPUT_PATH="hdfs://namenode:9000/user/input/spark_grades/grades.txt"
    OUTPUT_PATH="hdfs://namenode:9000/user/output/spark_grades_distribution"
    USE_HDFS=true
else
    echo "使用本地文件系统..."
    INPUT_PATH="../测试数据/grades.txt"
    OUTPUT_PATH="results/spark_output"
    USE_HDFS=false

    # Clean output directory
    rm -rf $OUTPUT_PATH
fi

echo "3. 运行Spark作业..."

if [ "$choice" == "3" ]; then
    # Python version
    if [ "$USE_HDFS" == "true" ]; then
        spark-submit \
            --master yarn \
            --deploy-mode cluster \
            --executor-memory 1g \
            --executor-cores 1 \
            --num-executors 2 \
            $PROGRAM \
            $INPUT_PATH \
            $OUTPUT_PATH
    else
        spark-submit \
            --master local[2] \
            $PROGRAM \
            $INPUT_PATH \
            $OUTPUT_PATH
    fi
else
    # Java/Scala version
    if [ "$USE_HDFS" == "true" ]; then
        spark-submit \
            --class $CLASS_NAME \
            --master yarn \
            --deploy-mode cluster \
            --executor-memory 1g \
            --executor-cores 1 \
            --num-executors 2 \
            $PROGRAM \
            $INPUT_PATH \
            $OUTPUT_PATH
    else
        spark-submit \
            --class $CLASS_NAME \
            --master local[2] \
            $PROGRAM \
            $INPUT_PATH \
            $OUTPUT_PATH
    fi
fi

if [ $? -ne 0 ]; then
    echo "Spark作业运行失败"
    exit 1
fi

echo "4. 查看结果..."

if [ "$USE_HDFS" == "true" ]; then
    echo "成绩分布分析结果:"
    hdfs dfs -cat $OUTPUT_PATH/part-00000

    echo "5. 下载结果到本地..."
    hdfs dfs -get $OUTPUT_PATH/part-00000 results/grade_distribution_analysis.txt
else
    echo "成绩分布分析结果:"
    cat $OUTPUT_PATH/part-00000
    cp $OUTPUT_PATH/part-00000 results/grade_distribution_analysis.txt
fi

echo "题目4完成! 结果已保存到 results/ 目录"
