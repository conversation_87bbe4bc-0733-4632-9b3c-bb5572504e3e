#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Grade Distribution Analysis using PySpark
Analyze required course average grades by class and score ranges:
90-100, 80-89, 70-79, 60-69, below 60
"""

import sys
from pyspark import SparkContext, SparkConf
from collections import defaultdict

def parse_line(line):
    """Parse a line from the input file"""
    fields = line.split(',')
    if len(fields) != 5:
        return None
    
    class_name = fields[0].strip()
    student_name = fields[1].strip()
    subject = fields[2].strip()
    course_type = fields[3].strip()
    grade_str = fields[4].strip()
    
    # Only process required courses
    if course_type != '必修':
        return None
    
    try:
        grade = float(grade_str)
        return (class_name, student_name, grade)
    except ValueError:
        return None

def categorize_grade(average):
    """Categorize grade into score ranges"""
    if average >= 90:
        return "90-100"
    elif average >= 80:
        return "80-89"
    elif average >= 70:
        return "70-79"
    elif average >= 60:
        return "60-69"
    else:
        return "60以下"

def format_class_result(class_data):
    """Format the result for a class"""
    class_name, category_data = class_data
    
    # Initialize counters
    category_counts = defaultdict(int)
    for category, count in category_data:
        category_counts[category] += count
    
    # Format output
    result = f"{class_name}: "
    result += f"90-100分: {category_counts['90-100']}人, "
    result += f"80-89分: {category_counts['80-89']}人, "
    result += f"70-79分: {category_counts['70-79']}人, "
    result += f"60-69分: {category_counts['60-69']}人, "
    result += f"60分以下: {category_counts['60以下']}人"
    
    return result

def main():
    if len(sys.argv) != 3:
        print("Usage: grade_distribution_analysis.py <input path> <output path>", file=sys.stderr)
        sys.exit(-1)
    
    input_path = sys.argv[1]
    output_path = sys.argv[2]
    
    # Initialize Spark
    conf = SparkConf().setAppName("Grade Distribution Analysis")
    sc = SparkContext(conf=conf)
    
    try:
        # Read input file
        lines = sc.textFile(input_path)
        
        # Filter and parse required courses
        required_courses = lines.map(parse_line).filter(lambda x: x is not None)
        
        # Calculate average grade for each student in required courses
        student_grades = required_courses.map(lambda x: ((x[0], x[1]), x[2])) \
                                       .groupByKey() \
                                       .map(lambda x: (x[0][0], sum(x[1]) / len(x[1])))
        
        # Categorize students by grade ranges
        grade_categories = student_grades.map(lambda x: ((x[0], categorize_grade(x[1])), 1))
        
        # Count students in each category by class
        category_counts = grade_categories.reduceByKey(lambda a, b: a + b)
        
        # Group by class and format output
        class_results = category_counts.map(lambda x: (x[0][0], (x[0][1], x[1]))) \
                                     .groupByKey() \
                                     .map(format_class_result)
        
        # Save results
        class_results.saveAsTextFile(output_path)
        
        print("Grade distribution analysis completed successfully!")
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)
    finally:
        sc.stop()

if __name__ == "__main__":
    main()
