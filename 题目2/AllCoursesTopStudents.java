import java.io.IOException;
import java.util.*;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.*;
import org.apache.hadoop.mapreduce.*;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;

/**
 * Find top 5 students by average grade (all courses) in each class
 */
public class AllCoursesTopStudents {

    // Job 1: Calculate average grade for all courses for each student
    public static class AllCoursesMapper extends Mapper<LongWritable, Text, Text, DoubleWritable> {
        
        @Override
        public void map(LongWritable key, Text value, Context context) 
                throws IOException, InterruptedException {
            
            String line = value.toString().trim();
            if (line.isEmpty()) return;
            
            String[] fields = line.split(",");
            if (fields.length != 5) return;
            
            String className = fields[0].trim();
            String studentName = fields[1].trim();
            String subject = fields[2].trim();
            String courseType = fields[3].trim();
            String gradeStr = fields[4].trim();
            
            try {
                double grade = Double.parseDouble(gradeStr);
                String studentKey = className + "," + studentName;
                context.write(new Text(studentKey), new DoubleWritable(grade));
            } catch (NumberFormatException e) {
                // Skip invalid grade entries
            }
        }
    }
    
    public static class AllCoursesReducer extends Reducer<Text, DoubleWritable, Text, DoubleWritable> {
        
        @Override
        public void reduce(Text key, Iterable<DoubleWritable> values, Context context)
                throws IOException, InterruptedException {
            
            double sum = 0.0;
            int count = 0;
            
            for (DoubleWritable value : values) {
                sum += value.get();
                count++;
            }
            
            if (count > 0) {
                double average = sum / count;
                context.write(key, new DoubleWritable(average));
            }
        }
    }
    
    // Job 2: Find top 5 students by average grade in each class
    public static class TopStudentsMapper extends Mapper<LongWritable, Text, Text, Text> {
        
        @Override
        public void map(LongWritable key, Text value, Context context) 
                throws IOException, InterruptedException {
            
            String line = value.toString().trim();
            if (line.isEmpty()) return;
            
            String[] parts = line.split("\t");
            if (parts.length != 2) return;
            
            String[] studentInfo = parts[0].split(",");
            if (studentInfo.length != 2) return;
            
            String className = studentInfo[0];
            String studentName = studentInfo[1];
            String averageGrade = parts[1];
            
            // Emit class as key, student info as value
            String studentData = studentName + "," + averageGrade;
            context.write(new Text(className), new Text(studentData));
        }
    }
    
    public static class TopStudentsReducer extends Reducer<Text, Text, Text, Text> {
        
        @Override
        public void reduce(Text key, Iterable<Text> values, Context context)
                throws IOException, InterruptedException {
            
            List<StudentGrade> students = new ArrayList<>();
            
            for (Text value : values) {
                String[] parts = value.toString().split(",");
                if (parts.length == 2) {
                    try {
                        String name = parts[0];
                        double grade = Double.parseDouble(parts[1]);
                        students.add(new StudentGrade(name, grade));
                    } catch (NumberFormatException e) {
                        // Skip invalid entries
                    }
                }
            }
            
            // Sort students by grade in descending order
            Collections.sort(students, new Comparator<StudentGrade>() {
                @Override
                public int compare(StudentGrade a, StudentGrade b) {
                    return Double.compare(b.grade, a.grade);
                }
            });
            
            // Get top 5 students
            StringBuilder result = new StringBuilder();
            int count = Math.min(5, students.size());
            for (int i = 0; i < count; i++) {
                if (i > 0) result.append("; ");
                result.append(students.get(i).name)
                      .append("(")
                      .append(String.format("%.2f", students.get(i).grade))
                      .append(")");
            }
            
            context.write(key, new Text(result.toString()));
        }
    }
    
    // Helper class for student grade information
    static class StudentGrade {
        String name;
        double grade;
        
        StudentGrade(String name, double grade) {
            this.name = name;
            this.grade = grade;
        }
    }
    
    public static void main(String[] args) throws Exception {
        if (args.length != 3) {
            System.err.println("Usage: AllCoursesTopStudents <input path> <intermediate output> <final output>");
            System.exit(-1);
        }
        
        Configuration conf = new Configuration();
        
        // Job 1: Calculate average grades for all courses
        Job job1 = Job.getInstance(conf, "all courses average");
        job1.setJarByClass(AllCoursesTopStudents.class);
        job1.setMapperClass(AllCoursesMapper.class);
        job1.setReducerClass(AllCoursesReducer.class);
        job1.setOutputKeyClass(Text.class);
        job1.setOutputValueClass(DoubleWritable.class);
        
        FileInputFormat.addInputPath(job1, new Path(args[0]));
        FileOutputFormat.setOutputPath(job1, new Path(args[1]));
        
        boolean job1Success = job1.waitForCompletion(true);
        if (!job1Success) {
            System.exit(1);
        }
        
        // Job 2: Find top 5 students in each class
        Job job2 = Job.getInstance(conf, "top students by class all courses");
        job2.setJarByClass(AllCoursesTopStudents.class);
        job2.setMapperClass(TopStudentsMapper.class);
        job2.setReducerClass(TopStudentsReducer.class);
        job2.setOutputKeyClass(Text.class);
        job2.setOutputValueClass(Text.class);
        
        FileInputFormat.addInputPath(job2, new Path(args[1]));
        FileOutputFormat.setOutputPath(job2, new Path(args[2]));
        
        System.exit(job2.waitForCompletion(true) ? 0 : 1);
    }
}
