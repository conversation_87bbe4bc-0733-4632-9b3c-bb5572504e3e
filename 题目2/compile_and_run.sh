#!/bin/bash

# Compile and run script for Topic 2 - Student Grade Analysis

echo "=== 题目2: 学生成绩分析 ==="

# Set Hadoop environment variables
export HADOOP_HOME=/opt/hadoop
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64

# Add Hadoop to PATH
export PATH=$HADOOP_HOME/bin:$HADOOP_HOME/sbin:$PATH

# Set Hadoop classpath
export HADOOP_CLASSPATH=$JAVA_HOME/lib/tools.jar

echo "1. 编译Java程序..."

# Compile StudentGradeAnalysis
echo "编译 StudentGradeAnalysis.java..."
hadoop com.sun.tools.javac.Main StudentGradeAnalysis.java
if [ $? -ne 0 ]; then
    echo "编译 StudentGradeAnalysis.java 失败"
    exit 1
fi

# Create JAR file
echo "创建JAR文件..."
jar cf student_grade_analysis.jar StudentGradeAnalysis*.class
if [ $? -ne 0 ]; then
    echo "创建JAR文件失败"
    exit 1
fi

# Compile AllCoursesTopStudents
echo "编译 AllCoursesTopStudents.java..."
hadoop com.sun.tools.javac.Main AllCoursesTopStudents.java
if [ $? -ne 0 ]; then
    echo "编译 AllCoursesTopStudents.java 失败"
    exit 1
fi

# Create JAR file
echo "创建JAR文件..."
jar cf all_courses_top_students.jar AllCoursesTopStudents*.class
if [ $? -ne 0 ]; then
    echo "创建JAR文件失败"
    exit 1
fi

echo "2. 准备HDFS输入数据..."

# Create input directory in HDFS
hdfs dfs -mkdir -p /user/input/grades
hdfs dfs -rm -r /user/output/grades* 2>/dev/null

# Copy input data to HDFS
hdfs dfs -put ../测试数据/grades.txt /user/input/grades/
if [ $? -ne 0 ]; then
    echo "上传输入数据失败"
    exit 1
fi

echo "3. 运行MapReduce作业..."

echo "3.1 运行必修课平均成绩分析..."
hadoop jar student_grade_analysis.jar StudentGradeAnalysis /user/input/grades /user/output/grades_intermediate /user/output/grades_required_top5
if [ $? -ne 0 ]; then
    echo "运行必修课分析失败"
    exit 1
fi

echo "3.2 运行所有课程平均成绩分析..."
hadoop jar all_courses_top_students.jar AllCoursesTopStudents /user/input/grades /user/output/grades_all_intermediate /user/output/grades_all_top5
if [ $? -ne 0 ]; then
    echo "运行所有课程分析失败"
    exit 1
fi

echo "4. 查看结果..."

echo "4.1 必修课平均成绩结果:"
hdfs dfs -cat /user/output/grades_intermediate/part-r-00000 | head -20

echo ""
echo "4.2 各班级必修课成绩前五名学生:"
hdfs dfs -cat /user/output/grades_required_top5/part-r-00000

echo ""
echo "4.3 各班级所有课程成绩前五名学生:"
hdfs dfs -cat /user/output/grades_all_top5/part-r-00000

echo "5. 下载结果到本地..."
mkdir -p results
hdfs dfs -get /user/output/grades_intermediate/part-r-00000 results/required_course_averages.txt
hdfs dfs -get /user/output/grades_required_top5/part-r-00000 results/required_course_top5.txt
hdfs dfs -get /user/output/grades_all_top5/part-r-00000 results/all_courses_top5.txt

echo "题目2完成! 结果已保存到 results/ 目录"
