# 分布式计算课程实验

本项目包含了分布式计算课程的三个实验题目，使用Java实现MapReduce和Spark程序。

## 实验环境

- Hadoop 3.x
- Spark 3.x  
- Java 8+
- Python 3.x (可选，用于Spark实验)
- Scala 2.12+ (可选，用于Spark实验)

## 题目说明

### 题目2: 学生成绩分析 (MapReduce)

**功能:**
1. 计算每个学生必修课的平均成绩
2. 统计每个班级中所有课程（必修+选修）平均成绩排名前五的学生姓名和成绩

**输入数据格式:**
```
班级,姓名,科目,课程类型,成绩
170315班,史伦泰,计算机图形学,选修,82
160301班,邱丰琪,计算机图形学,选修,90
```

**实现文件:**
- `StudentGradeAnalysis.java` - 必修课平均成绩分析
- `AllCoursesTopStudents.java` - 所有课程成绩前五名分析

### 题目3: 祖孙关系查找 (MapReduce)

**功能:**
找出所有具有grandchild-grandparent关系的人名对

**输入数据格式:**
```
子女姓名, 父母姓名
<PERSON>, <PERSON>, <PERSON>
```

**实现文件:**
- `GrandparentGrandchild.java` - 祖孙关系查找

### 题目4: 成绩分布分析 (Spark)

**功能:**
按班级统计必修课平均成绩在不同分数段的学生人数：
- 90~100分
- 80~89分  
- 70~79分
- 60~69分
- 60分以下

**实现文件:**
- `GradeDistributionAnalysis.java` - Java版本
- `GradeDistributionAnalysis.scala` - Scala版本
- `grade_distribution_analysis.py` - Python版本

## 使用方法

### 快速开始

1. 确保Hadoop和Spark环境已正确配置
2. 运行总脚本：
```bash
chmod +x run_all.sh
./run_all.sh
```

### 单独运行实验

#### 题目2
```bash
cd 题目2
chmod +x compile_and_run.sh
./compile_and_run.sh
```

#### 题目3
```bash
cd 题目3
chmod +x compile_and_run.sh
./compile_and_run.sh
```

#### 题目4
```bash
cd 题目4
chmod +x compile_and_run.sh
./compile_and_run.sh
```

## 输出结果

每个实验的结果都会保存在对应目录的`results/`文件夹中：

- **题目2结果:**
  - `required_course_averages.txt` - 学生必修课平均成绩
  - `required_course_top5.txt` - 各班级必修课前五名
  - `all_courses_top5.txt` - 各班级所有课程前五名

- **题目3结果:**
  - `grandparent_grandchild_relationships.txt` - 祖孙关系列表

- **题目4结果:**
  - `grade_distribution_analysis.txt` - 各班级成绩分布统计

## 程序设计思路

### 题目2 (MapReduce)
1. **第一个Job**: 计算每个学生的平均成绩
   - Mapper: 解析输入，过滤必修课/所有课程，输出(学生,成绩)
   - Reducer: 计算每个学生的平均成绩

2. **第二个Job**: 找出每个班级的前五名
   - Mapper: 解析第一个Job的输出，按班级分组
   - Reducer: 对每个班级的学生按成绩排序，取前五名

### 题目3 (MapReduce)
1. **第一个Job**: 处理父子关系
   - Mapper: 读取父子关系，同时输出子->父和父->子的关系
   - Reducer: 整理每个人的父母和子女信息

2. **第二个Job**: 查找祖孙关系
   - Mapper: 基于第一个Job的结果，寻找可能的祖孙关系
   - Reducer: 确认祖孙关系并输出

### 题目4 (Spark)
1. 过滤必修课程数据
2. 计算每个学生的必修课平均成绩
3. 将成绩分类到不同分数段
4. 按班级统计各分数段的学生人数
5. 格式化输出结果

## 注意事项

1. 确保Hadoop集群正常运行
2. 检查HDFS权限设置
3. 根据实际环境调整脚本中的路径配置
4. 如果集群模式失败，程序会自动切换到本地模式运行

## 测试数据

项目使用`测试数据/`目录中的数据文件：
- `grades.txt` - 学生成绩数据
- `child-parent.txt` - 父子关系数据

## 故障排除

1. **编译错误**: 检查Java版本和Hadoop/Spark环境变量
2. **HDFS错误**: 确保HDFS服务正常，检查权限设置
3. **内存不足**: 调整Spark executor内存配置
4. **网络问题**: 检查集群节点间的网络连接

## 扩展功能

- 支持多种编程语言实现
- 自动环境检测和配置
- 详细的错误处理和日志输出
- 结果验证和统计信息
