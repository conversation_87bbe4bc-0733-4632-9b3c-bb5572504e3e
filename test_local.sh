#!/bin/bash

# Local test script for quick verification without Hadoop cluster

echo "=========================================="
echo "本地测试脚本 (无需Hadoop集群)"
echo "=========================================="

# Create test directories
mkdir -p test_results

echo "测试题目4 - Spark本地模式..."

cd 题目4

# Test Python version (most likely to work in local mode)
if command -v spark-submit &> /dev/null && command -v python3 &> /dev/null; then
    echo "运行Python版本的Spark程序..."
    
    # Run in local mode
    spark-submit \
        --master local[2] \
        grade_distribution_analysis.py \
        ../测试数据/grades.txt \
        ../test_results/spark_python_output
    
    if [ $? -eq 0 ]; then
        echo "✓ Python版本运行成功"
        echo "结果:"
        cat ../test_results/spark_python_output/part-00000
    else
        echo "✗ Python版本运行失败"
    fi
else
    echo "⚠ Spark或Python3不可用，跳过测试"
fi

cd ..

echo ""
echo "测试简化版MapReduce逻辑..."

# Create a simple test for MapReduce logic using basic shell commands
echo "分析必修课成绩分布..."

# Extract required courses and calculate some basic statistics
if [ -f "测试数据/grades.txt" ]; then
    echo "必修课记录数:"
    grep "必修" 测试数据/grades.txt | wc -l
    
    echo ""
    echo "各班级必修课记录数:"
    grep "必修" 测试数据/grades.txt | cut -d',' -f1 | sort | uniq -c | head -10
    
    echo ""
    echo "必修课成绩分布 (前20个样本):"
    grep "必修" 测试数据/grades.txt | cut -d',' -f5 | sort -n | head -20
    
    echo ""
    echo "高分学生样本 (成绩>=90):"
    grep "必修" 测试数据/grades.txt | awk -F',' '$5 >= 90 {print $1 "," $2 "," $5}' | head -10
fi

echo ""
echo "分析父子关系..."

if [ -f "测试数据/child-parent.txt" ]; then
    echo "关系记录总数:"
    wc -l < 测试数据/child-parent.txt
    
    echo ""
    echo "样本关系 (前10个):"
    head -10 测试数据/child-parent.txt
    
    echo ""
    echo "可能的祖父母 (出现在父母位置的人):"
    cut -d',' -f2 测试数据/child-parent.txt | sort | uniq | head -10
fi

echo ""
echo "本地测试完成!"
echo "如需完整测试，请确保Hadoop/Spark集群正常运行后执行 ./run_all.sh"
